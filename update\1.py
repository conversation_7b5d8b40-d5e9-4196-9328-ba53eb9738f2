from tkinter import *
from PIL import Image, ImageTk

class LoginABPro:

    def __init__(self, root, isTKroot=True, params=None):
        uiName = Fun.GetUIName(root, self.__class__.__name__)
        self.uiName = uiName
        Fun.Register(uiName, 'UIClass', self)
        self.root = root
        self.configure_event = None
        self.isTKroot = isTKroot
        self.firstRun = True
        Fun.G_UIParamsDictionary[uiName] = params
        Fun.G_UICommandDictionary[uiName] = LoginABPro_cmd
        Fun.Register(uiName, 'root', root)
        style = LoginABPro_sty.SetupStyle(isTKroot)
        self.UIJsonString = '{"Version": "1.0.0", "UIName": "LoginABPro", "Description": "", "WindowSize": [606, 430], "WindowPosition": "Center", "WindowHide": false, "WindowResizable": false, "WindowTitle": "快剪AB", "DarkMode": true, "BorderWidth": 0, "BorderColor": "#00FFFF", "DropTitle": false, "DragWindow": true, "MinSize": [0, 0], "TransparentColor": "#00FFFF", "RootTransparency": 255, "ICOFile": null, "WinState": 1, "WinTopMost": false, "BGColor": "#EFEFEF", "GroupList": {}, "WidgetList": [{"Type": "Form", "Index": 1, "AliasName": "Form_1", "BGColor": "#EFEFEF", "Size": [606, 430], "WinRoundCorner": true, "EventList": {"Load": "Form_1_onLoad", "Exit": "Form_1_onExit", "Display": "Form_1_onDisplay", "InitCheck": "Form_1_onInitCheck"}}, {"Type": "Label", "Index": 10, "AliasName": "Label_5msg", "ParentName": "Form_1", "PlaceInfo": [2, 338, 590, 35, "nw", true, false], "Visible": true, "Size": [590, 35], "BGColor": "#EFEFEF", "Text": "Loading...", "FGColor": "#000000"}, {"Type": "Label", "Index": 14, "AliasName": "Label_3", "ParentName": "Form_1", "PlaceInfo": [40, 291, 44, 38, "nw", true, false], "Visible": true, "Size": [44, 38], "BGColor": "#EFEFEF", "Text": "", "FGColor": "#000000", "BGImage": "Resources/ICO_Team.png", "Compound": "left"}, {"Type": "Entry", "Index": 7, "AliasName": "Entry_1key", "ParentName": "Form_1", "PlaceInfo": [97, 292, 365, 35, "nw", true, false], "Visible": true, "Size": [365, 35], "BGColor": "#EFEFEF", "BGColor_ReadOnly": "#EFEFEF", "FGColor": "#000000", "InnerBorderColor": "#000000", "TipText": "请输入卡密！！！", "TipFGColor": "#888888", "Relief": "sunken", "EventList": {}}, {"Type": "Entry", "Index": 8, "AliasName": "Entry_2mac", "ParentName": "Form_1", "PlaceInfo": [0, -1, 346, 48, "nw", false, false], "Visible": false, "Size": [1, 1], "BGColor": "#EFEFEF", "BGColor_ReadOnly": "#EFEFEF", "FGColor": "#000000", "InnerBorderColor": "#000000", "TipFGColor": "#888888", "Relief": "sunken", "State": "readonly"}, {"Type": "LabelButton", "Index": 9, "AliasName": "LabelButton_1login", "ParentName": "Form_1", "PlaceInfo": [470, 292, 85, 35, "nw", true, false], "Visible": true, "Size": [85, 35], "Text": "登陆", "BGColor": "#EFEFEF", "FGColor": "#000000", "Font": ["华文楷体", 15, "bold", "roman", 0, 0], "BGImage": "Resources/BTN_darkgrey.png", "Compound": "center", "BGColor_Hover": "#EFEFEF", "FGColor_Hover": "#000000", "BGImage_Hover": "Resources/BTN_darkgrey_hover.png", "BGColor_Click": "#EFEFEF", "FGColor_Click": "#000000", "BGImage_Click": "Resources/BTN_darkgrey_click.png", "Droplist": false, "EventList": {"Command": "LabelButton_1login_onCommand"}}, {"Type": "CheckButton", "Index": 12, "AliasName": "CheckButton_1", "ParentName": "Form_1", "PlaceInfo": [137, 379, 346, 40, "nw", true, false], "Visible": true, "Size": [346, 40], "Value": false, "BGColor": "#EFEFEF", "ActiveBGColor": "#EFEFEF", "Text": "我已阅读并同意《用户协议》", "FGColor": "#000000", "ActiveFGColor": "#000000", "Anchor": "w", "Font": ["Microsoft YaHei UI", 10, "normal", "roman", 0, 0], "SelectColor": "#EFEFEF", "SelectBGColor": "#EFEFEF", "SelectFGColor": "#000000", "Relief": "flat", "EventList": {"Command": "CheckButton_1_onCommand"}}, {"Type": "LabelFrame", "Index": 15, "AliasName": "LabelFrame_1", "ParentName": "Form_1", "PlaceInfo": [2, 0, 600, 282, "nw", true, false], "Visible": true, "Size": [600, 282], "BGColor": "#EFEFEF", "Text": "更新公告", "Anchor": "nw", "Relief": "groove", "ScrollRegion": null}, {"Type": "ListBox", "Index": 16, "AliasName": "ListBox_1更新公告", "ParentName": "LabelFrame_15", "PlaceInfo": [4, 0, 587, 241, "nw", true, false], "Visible": true, "Size": [587, 241], "ExportSelection": 0, "BGColor": "#EFEFEF", "SelectMode": "EXTENDED"}]}'
        Form_1 = Fun.CreateUIFormJson(uiName, root, isTKroot, style, self.UIJsonString)
        Fun.InitElementData(uiName)
        Fun.RunForm1_CallBack(uiName, 'Load', LoginABPro_cmd.Form_1_onLoad)
        if self.isTKroot == True and Fun.GetElement(self.uiName, 'root'):
            self.root.protocol('WM_DELETE_WINDOW', self.Exit)
            self.root.bind('<Configure>', self.Configure)

    def GetRootSize(self):
        return Fun.GetUIRootSize(self.uiName)

    def GetAllElement(self):
        return Fun.G_UIElementDictionary[self.uiName]

    def Escape(self, event):
        if Fun.AskBox('提示', '确定退出程序？') == True:
            self.Exit()

    def Exit(self):
        Result = Fun.RunForm1_CallBack(self.uiName, 'Exit', LoginABPro_cmd.Form_1_onExit)
        if self.isTKroot == True and Result != False:
            Fun.DestroyUI(self.uiName, 0, '')

    def Configure(self, event):
        Form_1 = Fun.GetElement(self.uiName, 'Form_1')
        if Form_1 == event.widget:
            Fun.ReDrawCanvasRecord(self.uiName)
            if self.firstRun == True:
                Fun.PrepareDisplayUI(self.uiName, Form_1, LoginABPro_cmd.Form_1_onDisplay)
                self.firstRun = False
            if self.firstRun == True:
                Fun.PrepareDisplayUI(self.uiName, Form_1, LoginABPro_cmd.Form_1_onDisplay)
                self.firstRun = False
        if self.root == event.widget and (self.configure_event is None or self.configure_event[2] != event.width or self.configure_event[3] != event.height):
            uiName = self.uiName
            self.configure_event = [event.x, event.y, event.width, event.height]
            Fun.ResizeRoot(self.uiName, self.root, event)
            Fun.ResizeAllChart(self.uiName)
if __name__ == '__main__':
    Fun.RunApplication(LoginABPro, True, 'LoginABPro', LoginABPro_cmd.Form_1_onInitCheck)