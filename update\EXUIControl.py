# EXUIControl模块 - 扩展UI控件
import tkinter as tk

class EXUIControl:
    """扩展UI控件类"""
    
    def __init__(self):
        pass
    
    @staticmethod
    def create_label_button(parent, text, command=None, **kwargs):
        """创建标签按钮"""
        button = tk.<PERSON><PERSON>(parent, text=text, command=command, **kwargs)
        return button
    
    @staticmethod
    def create_entry_with_placeholder(parent, placeholder="", **kwargs):
        """创建带占位符的输入框"""
        entry = tk.Entry(parent, **kwargs)
        
        def on_focus_in(event):
            if entry.get() == placeholder:
                entry.delete(0, tk.END)
                entry.config(fg='black')
        
        def on_focus_out(event):
            if entry.get() == "":
                entry.insert(0, placeholder)
                entry.config(fg='grey')
        
        entry.insert(0, placeholder)
        entry.config(fg='grey')
        entry.bind('<FocusIn>', on_focus_in)
        entry.bind('<FocusOut>', on_focus_out)
        
        return entry
