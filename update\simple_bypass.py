#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AB超剪王 - 简单登录绕过器
最简化的绕过方案，直接启动主程序
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import os
import sys

def find_main_program():
    """查找主程序文件"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    
    # 可能的主程序路径
    possible_paths = [
        os.path.join(parent_dir, "AB超剪王.exe"),
        os.path.join(current_dir, "Main.exe"),
        os.path.join(current_dir, "..", "AB超剪王.exe"),
        os.path.join(parent_dir, "AB超剪王.py")
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return None

def start_main_program():
    """启动主程序"""
    main_exe_path = find_main_program()
    
    if main_exe_path:
        try:
            print(f"找到主程序: {main_exe_path}")
            print("正在启动主程序...")
            
            # 启动主程序
            if main_exe_path.endswith('.py'):
                subprocess.Popen([sys.executable, main_exe_path], 
                               cwd=os.path.dirname(main_exe_path))
            else:
                subprocess.Popen([main_exe_path], 
                               cwd=os.path.dirname(main_exe_path))
            
            print("主程序已启动！")
            print("登录验证已成功绕过。")
            return True
            
        except Exception as e:
            print(f"启动主程序失败: {e}")
            return False
    else:
        print("未找到主程序文件！")
        print("请确保以下文件存在：")
        print("- AB超剪王.exe")
        print("- Main.exe")
        return False

def create_gui():
    """创建图形界面"""
    root = tk.Tk()
    root.title("AB超剪王 - 登录绕过器")
    root.geometry("400x250")
    root.resizable(False, False)
    
    # 居中显示
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    # 设置背景色
    root.configure(bg='#f0f0f0')
    
    # 标题
    title_label = tk.Label(root, 
                          text="AB超剪王", 
                          font=("Microsoft YaHei", 18, "bold"),
                          fg="#2c3e50",
                          bg='#f0f0f0')
    title_label.pack(pady=20)
    
    # 状态信息
    status_label = tk.Label(root, 
                           text="✓ 登录验证已绕过", 
                           font=("Microsoft YaHei", 12, "bold"),
                           fg="#27ae60",
                           bg='#f0f0f0')
    status_label.pack(pady=10)
    
    info_label = tk.Label(root, 
                         text="无需输入卡密，直接启动程序", 
                         font=("Microsoft YaHei", 10),
                         fg="#34495e",
                         bg='#f0f0f0')
    info_label.pack(pady=5)
    
    def on_start_click():
        """启动按钮点击事件"""
        if start_main_program():
            messagebox.showinfo("成功", "主程序已启动！\n登录验证已成功绕过。")
            root.quit()
        else:
            messagebox.showerror("错误", "启动主程序失败！\n请检查程序文件是否存在。")
    
    def on_exit_click():
        """退出按钮点击事件"""
        root.quit()
    
    # 按钮框架
    button_frame = tk.Frame(root, bg='#f0f0f0')
    button_frame.pack(pady=30)
    
    # 启动按钮
    start_button = tk.Button(button_frame,
                            text="启动主程序",
                            font=("Microsoft YaHei", 12, "bold"),
                            command=on_start_click,
                            bg="#3498db",
                            fg="white",
                            padx=20,
                            pady=8,
                            relief="flat",
                            cursor="hand2")
    start_button.pack(side=tk.LEFT, padx=10)
    
    # 退出按钮
    exit_button = tk.Button(button_frame,
                           text="退出",
                           font=("Microsoft YaHei", 12),
                           command=on_exit_click,
                           bg="#e74c3c",
                           fg="white",
                           padx=20,
                           pady=8,
                           relief="flat",
                           cursor="hand2")
    exit_button.pack(side=tk.LEFT, padx=10)
    
    # 运行GUI
    try:
        root.mainloop()
    except KeyboardInterrupt:
        pass
    finally:
        try:
            root.destroy()
        except:
            pass

def main():
    """主函数"""
    print("=" * 50)
    print("AB超剪王 - 登录验证绕过器")
    print("=" * 50)
    print("正在启动...")
    
    # 检查是否有图形界面支持
    try:
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        
        # 启动图形界面
        create_gui()
        
    except Exception as e:
        print(f"无法启动图形界面: {e}")
        print("使用命令行模式...")
        
        # 命令行模式
        if start_main_program():
            print("\n按回车键退出...")
            input()
        else:
            print("\n启动失败，按回车键退出...")
            input()

if __name__ == "__main__":
    main()
