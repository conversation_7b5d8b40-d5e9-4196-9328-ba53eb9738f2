#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AB超剪王 - 综合破解工具
整合多种破解方法，自动选择最佳方案
"""

import os
import sys
import subprocess
import time
import shutil
import json
from datetime import datetime

class CrackTool:
    def __init__(self):
        self.exe_path = os.path.join(os.path.dirname(__file__), "Main.exe")
        self.backup_path = self.exe_path + ".backup"
        self.methods_tried = []
        self.success_method = None
        
    def check_environment(self):
        """检查运行环境"""
        print("检查运行环境...")
        
        # 检查文件是否存在
        if not os.path.exists(self.exe_path):
            print(f"❌ 未找到Main.exe文件: {self.exe_path}")
            return False
        
        print(f"✓ 找到目标文件: {self.exe_path}")
        print(f"  文件大小: {os.path.getsize(self.exe_path)} 字节")
        
        # 检查权限
        if not os.access(self.exe_path, os.R_OK | os.W_OK):
            print("❌ 没有文件读写权限")
            return False
        
        print("✓ 文件权限检查通过")
        
        # 检查是否已有备份
        if os.path.exists(self.backup_path):
            print("✓ 发现备份文件")
        else:
            print("ℹ 将创建备份文件")
        
        return True
    
    def create_backup(self):
        """创建备份"""
        try:
            if not os.path.exists(self.backup_path):
                shutil.copy2(self.exe_path, self.backup_path)
                print(f"✓ 已创建备份: {self.backup_path}")
            return True
        except Exception as e:
            print(f"❌ 创建备份失败: {e}")
            return False
    
    def method_1_simple_replacement(self):
        """方法1: 简单字符串替换"""
        print("\n=== 方法1: 简单字符串替换 ===")
        
        try:
            with open(self.exe_path, 'rb') as f:
                data = f.read()
            
            original_data = data
            modified_data = bytearray(data)
            changes = 0
            
            # 定义替换规则
            replacements = [
                (b'return False', b'return True '),
                (b'False', b'True '),
                (b'failed', b'passed'),
                (b'invalid', b'valid  '),
                (b'expired', b'valid  '),
                (b'error', b'ok   '),
                (b'deny', b'allow'),
                (b'fail', b'pass'),
            ]
            
            for old_bytes, new_bytes in replacements:
                if len(old_bytes) != len(new_bytes):
                    continue
                
                count = 0
                offset = 0
                while True:
                    pos = modified_data.find(old_bytes, offset)
                    if pos == -1:
                        break
                    
                    modified_data[pos:pos+len(old_bytes)] = new_bytes
                    count += 1
                    changes += 1
                    offset = pos + len(old_bytes)
                
                if count > 0:
                    print(f"  替换 '{old_bytes.decode('utf-8', errors='ignore')}' -> '{new_bytes.decode('utf-8', errors='ignore')}': {count} 次")
            
            if changes > 0:
                with open(self.exe_path, 'wb') as f:
                    f.write(modified_data)
                
                print(f"✓ 方法1完成，共进行 {changes} 处修改")
                return True
            else:
                print("❌ 方法1未找到可替换的内容")
                return False
                
        except Exception as e:
            print(f"❌ 方法1执行失败: {e}")
            return False
    
    def method_2_binary_patch(self):
        """方法2: 二进制补丁"""
        print("\n=== 方法2: 二进制补丁 ===")
        
        try:
            with open(self.exe_path, 'rb') as f:
                data = f.read()
            
            modified_data = bytearray(data)
            changes = 0
            
            # 查找并修改可能的条件跳转
            patterns = [
                (b'\x74', b'\xEB'),  # JE -> JMP (短跳转)
                (b'\x75', b'\x90'),  # JNE -> NOP
                (b'\x0F\x84', b'\x0F\x90'),  # JE -> NOP (长跳转，不完全正确但可能有效)
                (b'\x0F\x85', b'\x0F\x90'),  # JNE -> NOP
            ]
            
            for old_pattern, new_pattern in patterns:
                offset = 0
                count = 0
                
                while True:
                    pos = modified_data.find(old_pattern, offset)
                    if pos == -1:
                        break
                    
                    # 简单的启发式检查：确保在可能的代码段中
                    if 0x1000 < pos < len(modified_data) - 0x1000:
                        for i, byte in enumerate(new_pattern):
                            if pos + i < len(modified_data):
                                modified_data[pos + i] = byte
                        count += 1
                        changes += 1
                    
                    offset = pos + len(old_pattern)
                
                if count > 0:
                    print(f"  修改跳转指令 {old_pattern.hex()} -> {new_pattern.hex()}: {count} 次")
            
            if changes > 0:
                with open(self.exe_path, 'wb') as f:
                    f.write(modified_data)
                
                print(f"✓ 方法2完成，共进行 {changes} 处修改")
                return True
            else:
                print("❌ 方法2未找到可修改的跳转指令")
                return False
                
        except Exception as e:
            print(f"❌ 方法2执行失败: {e}")
            return False
    
    def method_3_nop_patch(self):
        """方法3: NOP填充可疑区域"""
        print("\n=== 方法3: NOP填充可疑区域 ===")
        
        try:
            with open(self.exe_path, 'rb') as f:
                data = f.read()
            
            modified_data = bytearray(data)
            changes = 0
            
            # 查找可能的验证相关字符串，并在附近填充NOP
            suspicious_strings = [
                b'license',
                b'verify',
                b'check',
                b'auth',
                b'valid',
                b'expire'
            ]
            
            for string in suspicious_strings:
                offset = 0
                while True:
                    pos = modified_data.find(string, offset)
                    if pos == -1:
                        break
                    
                    # 在字符串前后一定范围内查找可能的代码
                    start = max(0, pos - 50)
                    end = min(len(modified_data), pos + len(string) + 50)
                    
                    # 查找可能的函数调用或跳转指令
                    for i in range(start, end):
                        if i + 1 < len(modified_data):
                            # 查找CALL指令 (E8)
                            if modified_data[i] == 0xE8:
                                # 用NOP替换CALL指令
                                for j in range(5):  # CALL指令通常是5字节
                                    if i + j < len(modified_data):
                                        modified_data[i + j] = 0x90  # NOP
                                changes += 1
                                print(f"  NOP填充CALL指令: 偏移 0x{i:08X}")
                    
                    offset = pos + len(string)
            
            if changes > 0:
                with open(self.exe_path, 'wb') as f:
                    f.write(modified_data)
                
                print(f"✓ 方法3完成，共进行 {changes} 处修改")
                return True
            else:
                print("❌ 方法3未找到可修改的代码")
                return False
                
        except Exception as e:
            print(f"❌ 方法3执行失败: {e}")
            return False
    
    def test_crack_success(self):
        """测试破解是否成功"""
        print("\n=== 测试破解效果 ===")
        
        try:
            # 启动程序并等待一段时间
            print("启动程序进行测试...")
            process = subprocess.Popen([self.exe_path], 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE)
            
            # 等待3秒
            time.sleep(3)
            
            # 检查进程是否还在运行
            if process.poll() is None:
                print("✓ 程序成功启动并保持运行")
                
                # 尝试终止进程
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except:
                    try:
                        process.kill()
                    except:
                        pass
                
                return True
            else:
                # 程序已退出，检查退出代码
                stdout, stderr = process.communicate()
                print(f"❌ 程序退出，退出代码: {process.returncode}")
                
                if stdout:
                    print(f"标准输出: {stdout.decode('utf-8', errors='ignore')}")
                if stderr:
                    print(f"错误输出: {stderr.decode('utf-8', errors='ignore')}")
                
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    def restore_backup(self):
        """恢复备份"""
        try:
            if os.path.exists(self.backup_path):
                shutil.copy2(self.backup_path, self.exe_path)
                print("✓ 已恢复原文件")
                return True
            else:
                print("❌ 备份文件不存在")
                return False
        except Exception as e:
            print(f"❌ 恢复失败: {e}")
            return False
    
    def save_crack_info(self):
        """保存破解信息"""
        try:
            crack_info = {
                'timestamp': datetime.now().isoformat(),
                'exe_path': self.exe_path,
                'backup_path': self.backup_path,
                'methods_tried': self.methods_tried,
                'success_method': self.success_method,
                'file_size': os.path.getsize(self.exe_path)
            }
            
            with open("crack_info.json", 'w', encoding='utf-8') as f:
                json.dump(crack_info, f, ensure_ascii=False, indent=2)
            
            print("✓ 破解信息已保存到: crack_info.json")
            
        except Exception as e:
            print(f"❌ 保存破解信息失败: {e}")
    
    def run_crack_process(self):
        """运行完整的破解流程"""
        print("=" * 60)
        print("AB超剪王 - 自动破解工具")
        print("=" * 60)
        
        # 检查环境
        if not self.check_environment():
            return False
        
        # 创建备份
        if not self.create_backup():
            return False
        
        # 尝试各种破解方法
        methods = [
            ("简单字符串替换", self.method_1_simple_replacement),
            ("二进制补丁", self.method_2_binary_patch),
            ("NOP填充", self.method_3_nop_patch),
        ]
        
        for method_name, method_func in methods:
            print(f"\n尝试方法: {method_name}")
            self.methods_tried.append(method_name)
            
            # 恢复原文件
            self.restore_backup()
            
            # 应用当前方法
            if method_func():
                # 测试是否成功
                if self.test_crack_success():
                    print(f"🎉 破解成功！使用方法: {method_name}")
                    self.success_method = method_name
                    self.save_crack_info()
                    return True
                else:
                    print(f"❌ 方法 {method_name} 破解失败")
            else:
                print(f"❌ 方法 {method_name} 应用失败")
        
        # 所有方法都失败了
        print("\n❌ 所有破解方法都失败了")
        print("建议:")
        print("1. 检查是否有杀毒软件干扰")
        print("2. 尝试以管理员权限运行")
        print("3. 检查程序是否有其他保护机制")
        
        # 恢复原文件
        self.restore_backup()
        
        return False

def main():
    """主函数"""
    try:
        crack_tool = CrackTool()
        
        print("AB超剪王破解工具")
        print("此工具将尝试多种方法破解登录验证")
        print("\n警告: 此工具仅用于学习和研究目的")
        
        confirm = input("\n确定要继续吗? (y/N): ").strip().lower()
        if confirm != 'y':
            print("操作已取消")
            return
        
        # 运行破解流程
        success = crack_tool.run_crack_process()
        
        if success:
            print("\n🎉 破解完成！")
            print("现在可以运行Main.exe，应该能够绕过登录验证")
            
            # 创建启动脚本
            create_start_script()
        else:
            print("\n❌ 破解失败")
            print("请尝试其他方法或联系技术支持")
        
    except KeyboardInterrupt:
        print("\n程序被中断")
    except Exception as e:
        print(f"程序执行出错: {e}")

def create_start_script():
    """创建启动脚本"""
    script_content = '''@echo off
chcp 65001 >nul
title AB超剪王 - 破解版

echo ================================================
echo           AB超剪王 - 破解版
echo ================================================
echo.
echo 正在启动破解版程序...
echo 登录验证已被绕过
echo.

start "" "Main.exe"

echo 程序已启动！
echo.
pause
'''
    
    try:
        with open("start_cracked.bat", 'w', encoding='gbk') as f:
            f.write(script_content)
        print("✓ 已创建启动脚本: start_cracked.bat")
    except Exception as e:
        print(f"❌ 创建启动脚本失败: {e}")

if __name__ == "__main__":
    main()
