@echo off
chcp 65001 >nul
title AB超剪王 - 登录验证绕过器

echo ================================================
echo           AB超剪王 - 登录验证绕过器
echo ================================================
echo.
echo 正在启动程序...
echo 登录验证将被自动绕过，无需输入卡密
echo.

REM 尝试运行Python脚本
python "AB超剪王.py"

REM 如果Python脚本失败，尝试直接运行绕过器
if errorlevel 1 (
    echo.
    echo Python脚本启动失败，尝试直接运行绕过器...
    cd update
    python simple_bypass.py
    cd ..
)

REM 如果还是失败，尝试直接运行exe
if errorlevel 1 (
    echo.
    echo 绕过器启动失败，尝试直接运行主程序...
    cd update
    start Main.exe
    cd ..
)

echo.
echo 程序启动完成！
echo 如果程序没有正常启动，请检查Python环境是否正确安装。
echo.
pause
