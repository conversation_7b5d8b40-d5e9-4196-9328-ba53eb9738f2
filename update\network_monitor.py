#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络监控工具 - 监控Main.exe的网络请求
用于分析登录验证的网络通信
"""

import socket
import threading
import time
import json
import subprocess
import os
import sys
from datetime import datetime

class NetworkMonitor:
    def __init__(self):
        self.captured_requests = []
        self.monitoring = False
        self.target_process = "Main.exe"
        
    def start_monitoring(self):
        """开始监控网络请求"""
        print("开始监控网络请求...")
        self.monitoring = True
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=self._monitor_connections)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        return monitor_thread
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        print("停止监控网络请求")
    
    def _monitor_connections(self):
        """监控网络连接"""
        try:
            import psutil
            
            while self.monitoring:
                # 查找目标进程
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        if proc.info['name'] == self.target_process:
                            # 获取进程的网络连接
                            connections = proc.connections()
                            for conn in connections:
                                if conn.status == psutil.CONN_ESTABLISHED:
                                    self._log_connection(proc.info['pid'], conn)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                
                time.sleep(1)
                
        except ImportError:
            print("需要安装psutil库: pip install psutil")
        except Exception as e:
            print(f"监控过程中出错: {e}")
    
    def _log_connection(self, pid, connection):
        """记录网络连接"""
        conn_info = {
            'timestamp': datetime.now().isoformat(),
            'pid': pid,
            'local_addr': f"{connection.laddr.ip}:{connection.laddr.port}",
            'remote_addr': f"{connection.raddr.ip}:{connection.raddr.port}" if connection.raddr else "N/A",
            'status': connection.status,
            'type': connection.type
        }
        
        # 避免重复记录
        if conn_info not in self.captured_requests:
            self.captured_requests.append(conn_info)
            print(f"[{conn_info['timestamp']}] 网络连接: {conn_info['remote_addr']}")
    
    def save_captured_data(self, filename="network_capture.json"):
        """保存捕获的数据"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.captured_requests, f, ensure_ascii=False, indent=2)
            print(f"网络捕获数据已保存到: {filename}")
        except Exception as e:
            print(f"保存数据失败: {e}")
    
    def analyze_requests(self):
        """分析捕获的请求"""
        print("\n=== 网络请求分析 ===")
        
        if not self.captured_requests:
            print("没有捕获到网络请求")
            return
        
        # 统计远程地址
        remote_addrs = {}
        for req in self.captured_requests:
            addr = req['remote_addr']
            if addr != "N/A":
                remote_addrs[addr] = remote_addrs.get(addr, 0) + 1
        
        print(f"总共捕获 {len(self.captured_requests)} 个网络连接")
        print("\n远程地址统计:")
        for addr, count in sorted(remote_addrs.items(), key=lambda x: x[1], reverse=True):
            print(f"  {addr}: {count} 次连接")
        
        # 查找可能的验证服务器
        print("\n可能的验证服务器:")
        for addr in remote_addrs.keys():
            ip = addr.split(':')[0]
            if not self._is_local_ip(ip):
                print(f"  {addr} (外部服务器)")
    
    def _is_local_ip(self, ip):
        """判断是否为本地IP"""
        local_ranges = [
            '127.',
            '192.168.',
            '10.',
            '172.16.',
            '172.17.',
            '172.18.',
            '172.19.',
            '172.20.',
            '172.21.',
            '172.22.',
            '172.23.',
            '172.24.',
            '172.25.',
            '172.26.',
            '172.27.',
            '172.28.',
            '172.29.',
            '172.30.',
            '172.31.'
        ]
        
        return any(ip.startswith(prefix) for prefix in local_ranges)

def create_hosts_bypass():
    """创建hosts文件绕过方案"""
    print("\n=== 创建hosts绕过方案 ===")
    
    # 常见的验证域名
    common_domains = [
        'api.example.com',
        'auth.example.com',
        'verify.example.com',
        'license.example.com',
        'activation.example.com',
        'check.example.com'
    ]
    
    hosts_entries = []
    for domain in common_domains:
        hosts_entries.append(f"127.0.0.1 {domain}")
    
    hosts_content = "\n".join(hosts_entries)
    
    print("建议添加到hosts文件的内容:")
    print(hosts_content)
    
    # 保存到文件
    try:
        with open("hosts_bypass.txt", 'w', encoding='utf-8') as f:
            f.write("# AB超剪王 - hosts绕过方案\n")
            f.write("# 将以下内容添加到 C:\\Windows\\System32\\drivers\\etc\\hosts 文件中\n\n")
            f.write(hosts_content)
        
        print("\nhosts绕过方案已保存到: hosts_bypass.txt")
        print("使用方法:")
        print("1. 以管理员身份打开记事本")
        print("2. 打开文件: C:\\Windows\\System32\\drivers\\etc\\hosts")
        print("3. 将hosts_bypass.txt中的内容添加到hosts文件末尾")
        print("4. 保存文件并重启程序")
        
    except Exception as e:
        print(f"保存hosts文件失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("AB超剪王 - 网络监控和绕过工具")
    print("=" * 60)
    
    monitor = NetworkMonitor()
    
    print("\n选择操作:")
    print("1. 监控Main.exe的网络请求")
    print("2. 创建hosts绕过方案")
    print("3. 启动Main.exe并同时监控")
    print("4. 退出")
    
    try:
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == "1":
            # 仅监控
            monitor_thread = monitor.start_monitoring()
            
            print("监控已启动，请在另一个窗口运行Main.exe")
            print("按回车键停止监控...")
            input()
            
            monitor.stop_monitoring()
            monitor_thread.join(timeout=2)
            
            monitor.analyze_requests()
            monitor.save_captured_data()
            
        elif choice == "2":
            # 创建hosts绕过
            create_hosts_bypass()
            
        elif choice == "3":
            # 启动程序并监控
            monitor_thread = monitor.start_monitoring()
            
            print("正在启动Main.exe...")
            try:
                main_exe_path = os.path.join(os.path.dirname(__file__), "Main.exe")
                if os.path.exists(main_exe_path):
                    process = subprocess.Popen([main_exe_path])
                    
                    print("Main.exe已启动，正在监控网络请求...")
                    print("关闭Main.exe或按Ctrl+C停止监控")
                    
                    # 等待进程结束或用户中断
                    try:
                        process.wait()
                    except KeyboardInterrupt:
                        print("\n用户中断监控")
                        try:
                            process.terminate()
                        except:
                            pass
                else:
                    print("未找到Main.exe文件")
                    
            except Exception as e:
                print(f"启动Main.exe失败: {e}")
            
            monitor.stop_monitoring()
            monitor_thread.join(timeout=2)
            
            monitor.analyze_requests()
            monitor.save_captured_data()
            
        elif choice == "4":
            print("退出程序")
            
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n程序被中断")
    except Exception as e:
        print(f"程序执行出错: {e}")

if __name__ == "__main__":
    main()
