# 简化的Fun模块 - 绕过登录验证
import tkinter as tk
import tkinter.messagebox as messagebox
import json
import subprocess
import os

# 全局字典
G_UIElementDictionary = {}
G_UIParamsDictionary = {}
G_UICommandDictionary = {}
G_UIRootSizeDictionary = {}

def GetUIName(root, className):
    """获取UI名称"""
    return f"{className}_{id(root)}"

def Register(uiName, key, value):
    """注册UI元素"""
    if uiName not in G_UIElementDictionary:
        G_UIElementDictionary[uiName] = {}
    G_UIElementDictionary[uiName][key] = value

def GetElement(uiName, elementName):
    """获取UI元素"""
    if uiName in G_UIElementDictionary:
        return G_UIElementDictionary[uiName].get(elementName)
    return None

def CreateUIFormJson(uiName, root, isTKroot, style, jsonString):
    """创建UI表单 - 简化版本，直接跳过登录"""
    try:
        uiData = json.loads(jsonString)
        
        # 设置窗口标题
        if 'WindowTitle' in uiData:
            root.title(uiData['WindowTitle'])
        
        # 设置窗口大小
        if 'WindowSize' in uiData:
            width, height = uiData['WindowSize']
            root.geometry(f"{width}x{height}")
        
        # 设置窗口居中
        if uiData.get('WindowPosition') == 'Center':
            root.update_idletasks()
            x = (root.winfo_screenwidth() // 2) - (width // 2)
            y = (root.winfo_screenheight() // 2) - (height // 2)
            root.geometry(f"{width}x{height}+{x}+{y}")
        
        # 创建主框架
        main_frame = tk.Frame(root, bg=uiData.get('BGColor', '#EFEFEF'))
        main_frame.pack(fill='both', expand=True)
        
        # 注册主框架
        Register(uiName, 'Form_1', main_frame)
        
        # 显示绕过登录的消息
        success_label = tk.Label(main_frame, 
                                text="登录验证已绕过！程序启动成功！", 
                                font=("Microsoft YaHei", 16, "bold"),
                                fg="green",
                                bg=uiData.get('BGColor', '#EFEFEF'))
        success_label.pack(pady=50)
        
        # 添加启动主程序按钮
        start_button = tk.Button(main_frame,
                                text="启动主程序",
                                font=("Microsoft YaHei", 12),
                                command=lambda: start_main_program(),
                                bg="#4CAF50",
                                fg="white",
                                padx=20,
                                pady=10)
        start_button.pack(pady=20)
        
        return main_frame
        
    except Exception as e:
        print(f"创建UI失败: {e}")
        # 创建简单的成功界面
        main_frame = tk.Frame(root, bg='#EFEFEF')
        main_frame.pack(fill='both', expand=True)
        
        success_label = tk.Label(main_frame, 
                                text="登录验证已绕过！", 
                                font=("Microsoft YaHei", 16, "bold"),
                                fg="green",
                                bg='#EFEFEF')
        success_label.pack(pady=100)
        
        Register(uiName, 'Form_1', main_frame)
        return main_frame

def start_main_program():
    """启动主程序"""
    try:
        # 尝试启动主程序exe
        main_exe_path = os.path.join(os.path.dirname(__file__), "..", "AB超剪王.exe")
        if os.path.exists(main_exe_path):
            subprocess.Popen([main_exe_path])
        else:
            messagebox.showinfo("提示", "主程序文件未找到，但登录验证已成功绕过！")
    except Exception as e:
        messagebox.showinfo("提示", f"启动主程序失败: {e}\n但登录验证已成功绕过！")

def InitElementData(uiName):
    """初始化元素数据"""
    pass

def RunForm1_CallBack(uiName, eventType, callback):
    """运行表单回调 - 绕过所有验证"""
    if eventType == 'Load':
        print(f"表单加载: {uiName}")
    elif eventType == 'InitCheck':
        print(f"初始化检查: {uiName} - 验证已绕过")
        return True  # 直接返回成功
    elif eventType == 'Exit':
        print(f"表单退出: {uiName}")
    elif eventType == 'Display':
        print(f"表单显示: {uiName}")
    return True

def GetUIRootSize(uiName):
    """获取UI根窗口大小"""
    return G_UIRootSizeDictionary.get(uiName, (800, 600))

def AskBox(title, message):
    """询问对话框"""
    return messagebox.askyesno(title, message)

def DestroyUI(uiName, code, message):
    """销毁UI"""
    if uiName in G_UIElementDictionary:
        root = G_UIElementDictionary[uiName].get('root')
        if root:
            root.quit()
            root.destroy()

def ReDrawCanvasRecord(uiName):
    """重绘画布记录"""
    pass

def PrepareDisplayUI(uiName, form, callback):
    """准备显示UI"""
    pass

def ResizeRoot(uiName, root, event):
    """调整根窗口大小"""
    pass

def ResizeAllChart(uiName):
    """调整所有图表大小"""
    pass

def RunApplication(appClass, isTKroot, appName, initCallback):
    """运行应用程序 - 绕过登录验证"""
    print(f"启动应用程序: {appName}")
    print("正在绕过登录验证...")
    
    # 创建主窗口
    root = tk.Tk()
    root.title("AB超剪王 - 登录验证已绕过")
    
    try:
        # 直接创建应用实例，跳过初始化检查
        app = appClass(root, isTKroot)
        
        # 显示成功消息
        print("登录验证绕过成功！程序已启动。")
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        messagebox.showerror("错误", f"程序启动失败: {e}")
