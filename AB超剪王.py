
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AB超剪王 - 登录验证绕过版本
直接绕过登录验证，启动主程序
"""

import os
import subprocess
import sys
import tkinter as tk
from tkinter import messagebox

def launch_exe(exe_path):
    """启动exe文件"""
    try:
        process = subprocess.Popen(exe_path, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=False, creationflags=subprocess.CREATE_NO_WINDOW)
        return process
    except Exception as e:
        print(f'启动失败：{str(e)}')
        return None

def launch_bypass_gui():
    """启动绕过器图形界面"""
    try:
        bypass_script = os.path.join(os.getcwd(), 'update', 'simple_bypass.py')
        if os.path.exists(bypass_script):
            subprocess.Popen([sys.executable, bypass_script])
            return True
        else:
            print(f"绕过器脚本不存在: {bypass_script}")
            return False
    except Exception as e:
        print(f'启动绕过器失败：{e}')
        return False

def show_bypass_message():
    """显示绕过成功消息"""
    try:
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口

        result = messagebox.askyesno(
            "AB超剪王 - 登录验证绕过",
            "检测到登录验证已被绕过！\n\n"
            "是否直接启动主程序？\n\n"
            "点击'是'启动主程序\n"
            "点击'否'启动绕过器界面"
        )

        root.destroy()
        return result

    except Exception as e:
        print(f"显示消息失败: {e}")
        return True  # 默认启动主程序

if __name__ == '__main__':
    print("AB超剪王 - 登录验证绕过版本")
    print("正在启动...")

    original_dir = os.getcwd()
    exe_dir = f'{original_dir}/update'

    try:
        # 显示绕过消息
        start_main = show_bypass_message()

        if start_main:
            # 直接启动主程序
            print("正在启动主程序...")
            os.chdir(exe_dir)
            target_exe = 'Main.exe'
            process = launch_exe(target_exe)

            if process:
                print("主程序已启动！登录验证已绕过。")
            else:
                print("主程序启动失败，尝试启动绕过器界面...")
                os.chdir(original_dir)
                launch_bypass_gui()
        else:
            # 启动绕过器界面
            print("启动绕过器界面...")
            launch_bypass_gui()

    except Exception as e:
        print(f'程序执行失败：{e}')
        print("尝试启动绕过器界面...")
        try:
            os.chdir(original_dir)
            launch_bypass_gui()
        except:
            print("所有启动方式都失败了。")
            input("按回车键退出...")