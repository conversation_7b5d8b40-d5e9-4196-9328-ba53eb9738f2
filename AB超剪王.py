

import os
import subprocess

def launch_exe(exe_path):
    try:
        process = subprocess.Popen(exe_path, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=False, creationflags=subprocess.CREATE_NO_WINDOW)
        return process
    except Exception as e:
        print(f'启动失败：{str(e)}')
if __name__ == '__main__':
    original_dir = os.getcwd()
    exe_dir = f'{original_dir}/update'
    try:
        os.chdir(exe_dir)
        target_exe = 'Main.exe'
        launch_exe(target_exe)
    except Exception as e:
        print(f'EXE执行失败：{e}')