#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存补丁工具 - 运行时修改Main.exe的验证逻辑
通过内存修改绕过登录验证
"""

import ctypes
import ctypes.wintypes
import subprocess
import time
import os
import sys
import threading

# Windows API常量
PROCESS_ALL_ACCESS = 0x1F0FFF
MEM_COMMIT = 0x1000
MEM_RESERVE = 0x2000
PAGE_EXECUTE_READWRITE = 0x40
INFINITE = 0xFFFFFFFF

class MemoryPatcher:
    def __init__(self):
        self.target_process = None
        self.process_handle = None
        self.patches_applied = []
        
    def find_process(self, process_name):
        """查找目标进程"""
        try:
            import psutil
            
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'].lower() == process_name.lower():
                    return proc.info['pid']
            
            return None
            
        except ImportError:
            print("需要安装psutil库: pip install psutil")
            return None
        except Exception as e:
            print(f"查找进程失败: {e}")
            return None
    
    def attach_to_process(self, pid):
        """附加到目标进程"""
        try:
            # 打开进程句柄
            self.process_handle = ctypes.windll.kernel32.OpenProcess(
                PROCESS_ALL_ACCESS, False, pid
            )
            
            if not self.process_handle:
                print(f"无法打开进程 {pid}")
                return False
            
            print(f"成功附加到进程 {pid}")
            return True
            
        except Exception as e:
            print(f"附加进程失败: {e}")
            return False
    
    def read_memory(self, address, size):
        """读取进程内存"""
        try:
            buffer = ctypes.create_string_buffer(size)
            bytes_read = ctypes.wintypes.DWORD()
            
            success = ctypes.windll.kernel32.ReadProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                buffer,
                size,
                ctypes.byref(bytes_read)
            )
            
            if success:
                return buffer.raw[:bytes_read.value]
            else:
                return None
                
        except Exception as e:
            print(f"读取内存失败: {e}")
            return None
    
    def write_memory(self, address, data):
        """写入进程内存"""
        try:
            bytes_written = ctypes.wintypes.DWORD()
            
            success = ctypes.windll.kernel32.WriteProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                data,
                len(data),
                ctypes.byref(bytes_written)
            )
            
            if success and bytes_written.value == len(data):
                return True
            else:
                return False
                
        except Exception as e:
            print(f"写入内存失败: {e}")
            return False
    
    def find_pattern(self, pattern, start_address=0x400000, end_address=0x7FFFFFFF):
        """在内存中查找特定模式"""
        try:
            current_address = start_address
            chunk_size = 0x1000  # 4KB chunks
            
            while current_address < end_address:
                # 读取内存块
                data = self.read_memory(current_address, chunk_size)
                if data:
                    # 在数据中查找模式
                    offset = data.find(pattern)
                    if offset != -1:
                        return current_address + offset
                
                current_address += chunk_size
            
            return None
            
        except Exception as e:
            print(f"查找模式失败: {e}")
            return None
    
    def apply_login_bypass_patches(self):
        """应用登录绕过补丁"""
        print("正在应用登录绕过补丁...")
        
        # 常见的验证相关字符串模式
        patterns_to_patch = [
            # 可能的验证失败返回值
            b'\x00\x00\x00\x00',  # False (0)
            b'\x01\x00\x00\x00',  # True (1)
            
            # 可能的跳转指令 (x86)
            b'\x74',  # JE (Jump if Equal)
            b'\x75',  # JNE (Jump if Not Equal)
            b'\x0F\x84',  # JE (32-bit)
            b'\x0F\x85',  # JNE (32-bit)
        ]
        
        # 尝试查找和修改验证逻辑
        patches_applied = 0
        
        # 查找可能的验证函数
        verification_patterns = [
            b"license",
            b"verify",
            b"check",
            b"auth",
            b"login",
            b"valid"
        ]
        
        for pattern in verification_patterns:
            address = self.find_pattern(pattern)
            if address:
                print(f"找到可能的验证相关代码: 0x{address:08X}")
                
                # 尝试在附近查找条件跳转
                nearby_data = self.read_memory(address - 100, 200)
                if nearby_data:
                    # 查找条件跳转指令
                    for i, byte in enumerate(nearby_data):
                        if byte == 0x74 or byte == 0x75:  # JE or JNE
                            jump_address = address - 100 + i
                            print(f"找到条件跳转: 0x{jump_address:08X}")
                            
                            # 尝试修改跳转为无条件跳转或NOP
                            if self.patch_jump_instruction(jump_address):
                                patches_applied += 1
        
        print(f"应用了 {patches_applied} 个补丁")
        return patches_applied > 0
    
    def patch_jump_instruction(self, address):
        """修改跳转指令"""
        try:
            # 读取原始指令
            original = self.read_memory(address, 2)
            if not original:
                return False
            
            # 保存原始数据用于恢复
            self.patches_applied.append({
                'address': address,
                'original': original,
                'patched': b'\x90\x90'  # NOP NOP
            })
            
            # 应用补丁 (用NOP替换条件跳转)
            if self.write_memory(address, b'\x90\x90'):
                print(f"成功修改地址 0x{address:08X}")
                return True
            else:
                return False
                
        except Exception as e:
            print(f"修改跳转指令失败: {e}")
            return False
    
    def restore_patches(self):
        """恢复所有补丁"""
        print("正在恢复原始代码...")
        
        for patch in self.patches_applied:
            self.write_memory(patch['address'], patch['original'])
        
        self.patches_applied.clear()
        print("所有补丁已恢复")
    
    def detach(self):
        """分离进程"""
        if self.process_handle:
            ctypes.windll.kernel32.CloseHandle(self.process_handle)
            self.process_handle = None
            print("已分离进程")

def create_dll_injector():
    """创建DLL注入器"""
    dll_code = '''
#include <windows.h>
#include <stdio.h>

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
        case DLL_PROCESS_ATTACH:
            // 在这里执行绕过逻辑
            MessageBoxA(NULL, "登录验证已绕过！", "AB超剪王破解", MB_OK);
            break;
        case DLL_PROCESS_DETACH:
            break;
    }
    return TRUE;
}

// 导出函数 - 绕过验证
__declspec(dllexport) BOOL BypassLogin() {
    // 返回登录成功
    return TRUE;
}

// 导出函数 - 检查许可证
__declspec(dllexport) BOOL CheckLicense(const char* license) {
    // 总是返回许可证有效
    return TRUE;
}
'''
    
    try:
        with open("bypass_dll.c", 'w', encoding='utf-8') as f:
            f.write(dll_code)
        
        print("DLL源代码已生成: bypass_dll.c")
        print("编译命令: gcc -shared -o bypass.dll bypass_dll.c")
        
    except Exception as e:
        print(f"生成DLL代码失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("AB超剪王 - 内存补丁工具")
    print("=" * 60)
    
    patcher = MemoryPatcher()
    
    print("\n选择操作:")
    print("1. 启动Main.exe并应用内存补丁")
    print("2. 附加到正在运行的Main.exe")
    print("3. 生成DLL注入器代码")
    print("4. 退出")
    
    try:
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == "1":
            # 启动程序并补丁
            print("正在启动Main.exe...")
            
            main_exe_path = os.path.join(os.path.dirname(__file__), "Main.exe")
            if not os.path.exists(main_exe_path):
                print("未找到Main.exe文件")
                return
            
            try:
                # 启动进程
                process = subprocess.Popen([main_exe_path])
                time.sleep(2)  # 等待进程启动
                
                # 查找进程
                pid = patcher.find_process("Main.exe")
                if not pid:
                    print("未找到Main.exe进程")
                    return
                
                # 附加并应用补丁
                if patcher.attach_to_process(pid):
                    patcher.apply_login_bypass_patches()
                    
                    print("补丁已应用，程序应该可以绕过登录验证")
                    print("按回车键恢复补丁并退出...")
                    input()
                    
                    patcher.restore_patches()
                
            except Exception as e:
                print(f"操作失败: {e}")
            finally:
                patcher.detach()
                
        elif choice == "2":
            # 附加到现有进程
            pid = patcher.find_process("Main.exe")
            if not pid:
                print("未找到正在运行的Main.exe进程")
                return
            
            if patcher.attach_to_process(pid):
                patcher.apply_login_bypass_patches()
                
                print("补丁已应用到正在运行的进程")
                print("按回车键恢复补丁并退出...")
                input()
                
                patcher.restore_patches()
                patcher.detach()
                
        elif choice == "3":
            # 生成DLL注入器
            create_dll_injector()
            
        elif choice == "4":
            print("退出程序")
            
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n程序被中断")
        if patcher.process_handle:
            patcher.restore_patches()
            patcher.detach()
    except Exception as e:
        print(f"程序执行出错: {e}")

if __name__ == "__main__":
    # 检查是否以管理员权限运行
    try:
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            print("警告: 建议以管理员权限运行此工具以获得更好的效果")
    except:
        pass
    
    main()
