# created by tools/loadICU.tcl -- do not edit
namespace eval ::tcl::clock {
    ::msgcat::mcset id DAYS_OF_WEEK_ABBREV [list \
        "Min"\
        "Sen"\
        "Se<PERSON>"\
        "Rab"\
        "<PERSON><PERSON>"\
        "Ju<PERSON>"\
        "Sab"]
    ::msgcat::mcset id DAYS_OF_WEEK_FULL [list \
        "<PERSON>gu"\
        "Senin"\
        "<PERSON><PERSON><PERSON>"\
        "<PERSON><PERSON>"\
        "<PERSON><PERSON>"\
        "<PERSON><PERSON>"\
        "Sabtu"]
    ::msgcat::mcset id MONTHS_ABBREV [list \
        "Jan"\
        "Peb"\
        "Mar"\
        "Apr"\
        "Mei"\
        "Jun"\
        "Jul"\
        "Agu"\
        "Sep"\
        "Okt"\
        "Nov"\
        "Des"\
        ""]
    ::msgcat::mcset id MONTHS_FULL [list \
        "<PERSON><PERSON>ri"\
        "<PERSON><PERSON><PERSON><PERSON>"\
        "Mare<PERSON>"\
        "April"\
        "Mei"\
        "Juni"\
        "Juli"\
        "<PERSON><PERSON><PERSON>"\
        "September"\
        "Oktober"\
        "November"\
        "Desember"\
        ""]
}
