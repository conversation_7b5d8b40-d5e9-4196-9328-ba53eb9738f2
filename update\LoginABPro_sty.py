# LoginABPro_sty模块 - 样式设置
import tkinter.ttk as ttk

def SetupStyle(isTKroot):
    """设置样式"""
    try:
        style = ttk.Style()
        
        # 设置主题
        if 'vista' in style.theme_names():
            style.theme_use('vista')
        elif 'clam' in style.theme_names():
            style.theme_use('clam')
        else:
            style.theme_use('default')
        
        # 配置样式
        style.configure('TLabel', background='#EFEFEF', foreground='black')
        style.configure('TButton', background='#EFEFEF', foreground='black')
        style.configure('TEntry', background='white', foreground='black')
        style.configure('TFrame', background='#EFEFEF')
        
        return style
        
    except Exception as e:
        print(f"设置样式失败: {e}")
        return None
