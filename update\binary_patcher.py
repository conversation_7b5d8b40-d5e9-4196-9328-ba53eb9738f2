#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
二进制补丁工具 - 直接修改Main.exe文件
通过修改二进制文件绕过登录验证
"""

import os
import shutil
import struct
import hashlib
from datetime import datetime

class BinaryPatcher:
    def __init__(self, exe_path):
        self.exe_path = exe_path
        self.backup_path = exe_path + ".backup"
        self.patches_applied = []
        
    def create_backup(self):
        """创建原文件备份"""
        try:
            if not os.path.exists(self.backup_path):
                shutil.copy2(self.exe_path, self.backup_path)
                print(f"已创建备份文件: {self.backup_path}")
            else:
                print("备份文件已存在")
            return True
        except Exception as e:
            print(f"创建备份失败: {e}")
            return False
    
    def restore_backup(self):
        """恢复原文件"""
        try:
            if os.path.exists(self.backup_path):
                shutil.copy2(self.backup_path, self.exe_path)
                print("已恢复原文件")
                return True
            else:
                print("备份文件不存在")
                return False
        except Exception as e:
            print(f"恢复文件失败: {e}")
            return False
    
    def read_file(self):
        """读取文件内容"""
        try:
            with open(self.exe_path, 'rb') as f:
                return f.read()
        except Exception as e:
            print(f"读取文件失败: {e}")
            return None
    
    def write_file(self, data):
        """写入文件内容"""
        try:
            with open(self.exe_path, 'wb') as f:
                f.write(data)
            return True
        except Exception as e:
            print(f"写入文件失败: {e}")
            return False
    
    def find_patterns(self, data, patterns):
        """在数据中查找模式"""
        found_patterns = []
        
        for pattern_name, pattern_bytes in patterns.items():
            offset = 0
            while True:
                pos = data.find(pattern_bytes, offset)
                if pos == -1:
                    break
                
                found_patterns.append({
                    'name': pattern_name,
                    'offset': pos,
                    'pattern': pattern_bytes,
                    'length': len(pattern_bytes)
                })
                
                offset = pos + 1
        
        return found_patterns
    
    def apply_login_bypass_patches(self):
        """应用登录绕过补丁"""
        print("正在分析和修改Main.exe...")
        
        # 创建备份
        if not self.create_backup():
            return False
        
        # 读取文件
        data = self.read_file()
        if not data:
            return False
        
        print(f"文件大小: {len(data)} 字节")
        
        # 定义要查找和替换的模式
        patterns_to_find = {
            # Python字节码中可能的验证相关字符串
            'license_check': b'license',
            'verify_check': b'verify',
            'auth_check': b'auth',
            'login_check': b'login',
            'valid_check': b'valid',
            'check_key': b'check',
            'activation': b'activation',
            'expired': b'expired',
            'invalid': b'invalid',
            
            # 可能的网络请求相关
            'http_request': b'http',
            'https_request': b'https',
            'api_call': b'api',
            'server_url': b'server',
            
            # 可能的错误消息
            'error_msg': b'error',
            'failed_msg': b'failed',
            'success_msg': b'success',
            
            # Python相关的验证模式
            'return_false': b'return False',
            'return_true': b'return True',
            'if_not': b'if not',
            'raise_exception': b'raise',
        }
        
        # 查找模式
        found_patterns = self.find_patterns(data, patterns_to_find)
        
        if not found_patterns:
            print("未找到可识别的验证模式")
            return False
        
        print(f"找到 {len(found_patterns)} 个可能的验证相关模式:")
        for pattern in found_patterns:
            print(f"  {pattern['name']}: 偏移 0x{pattern['offset']:08X}")
        
        # 应用补丁
        modified_data = bytearray(data)
        patches_applied = 0
        
        # 策略1: 将可能的验证失败替换为成功
        replacements = {
            b'return False': b'return True ',  # 注意保持长度一致
            b'False': b'True ',
            b'failed': b'passed',
            b'invalid': b'valid  ',  # 保持长度
            b'expired': b'valid  ',  # 保持长度
            b'error': b'ok   ',     # 保持长度
        }
        
        for old_bytes, new_bytes in replacements.items():
            if len(old_bytes) != len(new_bytes):
                print(f"警告: 替换长度不匹配 {old_bytes} -> {new_bytes}")
                continue
            
            offset = 0
            while True:
                pos = modified_data.find(old_bytes, offset)
                if pos == -1:
                    break
                
                # 应用替换
                modified_data[pos:pos+len(old_bytes)] = new_bytes
                patches_applied += 1
                
                print(f"替换: 偏移 0x{pos:08X} - {old_bytes} -> {new_bytes}")
                
                self.patches_applied.append({
                    'offset': pos,
                    'original': old_bytes,
                    'patched': new_bytes
                })
                
                offset = pos + len(old_bytes)
        
        # 策略2: 修改可能的条件跳转 (如果是x86机器码)
        # 查找常见的条件跳转指令并替换为无条件跳转或NOP
        jump_patterns = {
            b'\x74': b'\xEB',  # JE -> JMP
            b'\x75': b'\x90',  # JNE -> NOP
        }
        
        for old_jump, new_jump in jump_patterns.items():
            offset = 0
            while True:
                pos = modified_data.find(old_jump, offset)
                if pos == -1:
                    break
                
                # 检查是否在合理的代码段范围内
                if 0x1000 < pos < len(modified_data) - 0x1000:
                    modified_data[pos] = new_jump[0]
                    patches_applied += 1
                    
                    print(f"修改跳转: 偏移 0x{pos:08X}")
                    
                    self.patches_applied.append({
                        'offset': pos,
                        'original': old_jump,
                        'patched': new_jump
                    })
                
                offset = pos + 1
        
        if patches_applied == 0:
            print("未应用任何补丁")
            return False
        
        # 写入修改后的文件
        if self.write_file(modified_data):
            print(f"成功应用 {patches_applied} 个补丁")
            
            # 保存补丁信息
            self.save_patch_info()
            
            return True
        else:
            print("写入修改后的文件失败")
            return False
    
    def save_patch_info(self):
        """保存补丁信息"""
        try:
            patch_info = {
                'timestamp': datetime.now().isoformat(),
                'exe_path': self.exe_path,
                'backup_path': self.backup_path,
                'patches': self.patches_applied
            }
            
            import json
            with open("patch_info.json", 'w', encoding='utf-8') as f:
                json.dump(patch_info, f, ensure_ascii=False, indent=2)
            
            print("补丁信息已保存到: patch_info.json")
            
        except Exception as e:
            print(f"保存补丁信息失败: {e}")
    
    def verify_patches(self):
        """验证补丁是否成功应用"""
        data = self.read_file()
        if not data:
            return False
        
        print("验证补丁应用情况:")
        
        for patch in self.patches_applied:
            offset = patch['offset']
            expected = patch['patched']
            actual = data[offset:offset+len(expected)]
            
            if actual == expected:
                print(f"✓ 偏移 0x{offset:08X}: 补丁已正确应用")
            else:
                print(f"✗ 偏移 0x{offset:08X}: 补丁应用失败")
                print(f"  期望: {expected}")
                print(f"  实际: {actual}")
        
        return True

def create_launcher_script():
    """创建启动脚本"""
    launcher_code = '''@echo off
chcp 65001 >nul
title AB超剪王 - 破解版启动器

echo ================================================
echo           AB超剪王 - 破解版启动器
echo ================================================
echo.
echo 正在启动破解版程序...
echo 登录验证已被绕过
echo.

REM 启动破解后的Main.exe
start "" "Main.exe"

echo 程序已启动！
echo 如果遇到问题，请运行 restore_backup.bat 恢复原文件
echo.
pause
'''
    
    try:
        with open("start_cracked.bat", 'w', encoding='gbk') as f:
            f.write(launcher_code)
        
        # 创建恢复脚本
        restore_code = '''@echo off
chcp 65001 >nul
title AB超剪王 - 恢复原文件

echo ================================================
echo           AB超剪王 - 恢复原文件
echo ================================================
echo.

if exist "Main.exe.backup" (
    echo 正在恢复原文件...
    copy /Y "Main.exe.backup" "Main.exe"
    echo 原文件已恢复！
) else (
    echo 未找到备份文件 Main.exe.backup
)

echo.
pause
'''
        
        with open("restore_backup.bat", 'w', encoding='gbk') as f:
            f.write(restore_code)
        
        print("启动脚本已创建:")
        print("  start_cracked.bat - 启动破解版程序")
        print("  restore_backup.bat - 恢复原文件")
        
    except Exception as e:
        print(f"创建启动脚本失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("AB超剪王 - 二进制补丁工具")
    print("=" * 60)
    
    exe_path = os.path.join(os.path.dirname(__file__), "Main.exe")
    
    if not os.path.exists(exe_path):
        print(f"未找到Main.exe文件: {exe_path}")
        return
    
    patcher = BinaryPatcher(exe_path)
    
    print(f"目标文件: {exe_path}")
    print(f"文件大小: {os.path.getsize(exe_path)} 字节")
    
    print("\n选择操作:")
    print("1. 应用登录绕过补丁")
    print("2. 恢复原文件")
    print("3. 验证补丁")
    print("4. 创建启动脚本")
    print("5. 退出")
    
    try:
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == "1":
            # 应用补丁
            print("\n警告: 此操作将修改Main.exe文件")
            confirm = input("确定要继续吗? (y/N): ").strip().lower()
            
            if confirm == 'y':
                if patcher.apply_login_bypass_patches():
                    print("\n补丁应用成功！")
                    print("现在可以尝试运行Main.exe，应该能够绕过登录验证")
                    
                    # 创建启动脚本
                    create_launcher_script()
                else:
                    print("\n补丁应用失败")
            else:
                print("操作已取消")
                
        elif choice == "2":
            # 恢复原文件
            if patcher.restore_backup():
                print("原文件已恢复")
            
        elif choice == "3":
            # 验证补丁
            patcher.verify_patches()
            
        elif choice == "4":
            # 创建启动脚本
            create_launcher_script()
            
        elif choice == "5":
            print("退出程序")
            
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n程序被中断")
    except Exception as e:
        print(f"程序执行出错: {e}")

if __name__ == "__main__":
    main()
